use tauri::State;
use chrono::Utc;
use sysinfo::{System, SystemExt, ProcessExt};

use crate::{AppState, models::*};

#[tauri::command]
pub async fn get_app_info(
    state: State<'_, AppState>,
) -> Result<AppInfo, String> {
    let uptime_seconds = state.startup_time.elapsed().as_secs();
    let startup_time_ms = 3000; // Placeholder - would be calculated during startup
    
    // Get database size (simplified)
    let database_size_mb = 10.0; // Placeholder
    
    let cache_size = state.cache_manager.read().await.get_cache_size();
    
    Ok(AppInfo {
        version: env!("CARGO_PKG_VERSION").to_string(),
        startup_time_ms,
        uptime_seconds,
        database_size_mb,
        cache_size,
    })
}

#[tauri::command]
pub async fn get_performance_metrics(
    state: State<'_, AppState>,
) -> Result<PerformanceMetrics, String> {
    let cache_stats = state.cache_manager.read().await.get_stats().await;
    
    // Get system metrics
    let mut system = System::new_all();
    system.refresh_all();
    
    let memory_usage_mb = if let Some(process) = system.processes_by_exact_name("poe-profit-ai").next() {
        process.memory() as f64 / 1024.0 / 1024.0
    } else {
        0.0
    };
    
    let cpu_usage_percent = if let Some(process) = system.processes_by_exact_name("poe-profit-ai").next() {
        process.cpu_usage() as f64
    } else {
        0.0
    };
    
    Ok(PerformanceMetrics {
        avg_query_time_ms: 50.0, // Placeholder - would track actual query times
        cache_hit_rate: cache_stats.hit_rate(),
        memory_usage_mb,
        cpu_usage_percent,
        active_connections: 1,
        last_updated: Utc::now(),
    })
}

#[tauri::command]
pub async fn force_data_sync(
    state: State<'_, AppState>,
) -> Result<String, String> {
    let mut sync_scheduler = state.sync_scheduler.write().await;

    if let Some(scheduler) = sync_scheduler.as_mut() {
        scheduler.trigger_manual_sync().await
            .map_err(|e| format!("Sync failed: {}", e))?;
        
        Ok("Data sync completed successfully".to_string())
    } else {
        Err("Sync scheduler not available".to_string())
    }
}

#[tauri::command]
pub async fn get_sync_status(
    state: State<'_, AppState>,
) -> Result<SyncStatus, String> {
    let sync_scheduler = state.sync_scheduler.read().await;
    
    if let Some(scheduler) = sync_scheduler.as_ref() {
        Ok(scheduler.get_sync_status())
    } else {
        Ok(SyncStatus {
            is_syncing: false,
            last_sync: None,
            last_error: Some("Scheduler not initialized".to_string()),
            items_synced: 0,
            sync_duration_ms: 0,
            next_sync_eta: None,
        })
    }
}
